import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { TreatmentType } from '@/types/calendar';
import { DiagnosticEvent, DiagnosticEventFilters, CreateDiagnosticEventData } from '@/types/notifications';
import { notificationService } from '@/services/notificationService';
import { getPlants } from '@/services/api';
import { Plant } from '@/types';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/common/Spinner';
import { CalendarIcon, PlusIcon, FilterIcon, XMarkIcon } from '@/components/common/icons';
import { BackgroundWrapper } from '@/components/common/BackgroundWrapper';
import { motion, AnimatePresence } from 'framer-motion';

/**
 * Composant modal pour créer un nouvel événement
 */
const CreateEventModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onCreateEvent: (eventData: CreateDiagnosticEventData) => Promise<void>;
  plants: Plant[];
}> = ({ isOpen, onClose, onCreateEvent, plants }) => {
  const [formData, setFormData] = useState({
    plantId: '',
    title: '',
    description: '',
    eventType: 'traitement' as 'diagnostic' | 'traitement' | 'suivi',
    treatmentType: 'arrosage' as TreatmentType,
    nextActionDate: new Date(),
    nextActionType: '',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
    isRecurring: false,
    recurrenceFrequency: 'weekly' as 'daily' | 'weekly' | 'monthly',
    recurrenceInterval: 1,
    recurrenceEndDate: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.plantId || !formData.title) {
      console.error('❌ Champs requis manquants:', `plantId: ${formData.plantId || 'manquant'}, title: ${formData.title || 'manquant'}`);
      return;
    }

    setIsSubmitting(true);
    try {
      const selectedPlant = plants.find(p => p.id === formData.plantId);
      if (!selectedPlant) return;

      await onCreateEvent({
        userId: '', // Sera rempli par le composant parent
        plantId: formData.plantId,
        plantName: selectedPlant.name,
        title: formData.title,
        description: formData.description,
        eventType: formData.eventType,
        treatmentType: formData.treatmentType,
        nextActionDate: formData.nextActionDate,
        nextActionType: formData.nextActionType || `${formData.eventType} - ${formData.treatmentType}`,
        priority: formData.priority,
        isRecurring: formData.isRecurring,
        recurrencePattern: formData.isRecurring ? {
          frequency: formData.recurrenceFrequency,
          interval: formData.recurrenceInterval,
          endDate: formData.recurrenceEndDate ? new Date(formData.recurrenceEndDate) : undefined
        } : undefined
      });
      onClose();
      // Réinitialiser le formulaire
      setFormData({
        plantId: '',
        title: '',
        description: '',
        eventType: 'traitement',
        treatmentType: 'arrosage',
        nextActionDate: new Date(),
        nextActionType: '',
        priority: 'medium',
        isRecurring: false,
        recurrenceFrequency: 'weekly',
        recurrenceInterval: 1,
        recurrenceEndDate: ''
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la création de l\'événement:', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-[#1c1a31] rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">Nouvel événement</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <XMarkIcon className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Sélection de la plante */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Plante *
            </label>
            <select
              value={formData.plantId}
              onChange={(e) => setFormData(prev => ({ ...prev, plantId: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
              required
            >
              <option value="">Sélectionner une plante</option>
              {plants.map(plant => (
                <option key={plant.id} value={plant.id}>{plant.name}</option>
              ))}
            </select>
          </div>

          {/* Titre */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Titre *
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
              placeholder="Ex: Arrosage hebdomadaire"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white h-20"
              placeholder="Détails de l'action à effectuer..."
            />
          </div>

          {/* Type d'événement */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Type d'événement
            </label>
            <select
              value={formData.eventType}
              onChange={(e) => setFormData(prev => ({ ...prev, eventType: e.target.value as any }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
            >
              <option value="arrosage">Arrosage</option>
              <option value="diagnostic">Diagnostic</option>
              <option value="traitement">Traitement</option>
              <option value="suivi">Suivi</option>
              <option value="rappel">Rappel</option>
              <option value="observation">Observation</option>
              <option value="prevention">Prévention</option>
              <option value="urgence">Urgence</option>
              <option value="maintenance">Maintenance</option>
              <option value="controle">Contrôle</option>
              <option value="evaluation">Évaluation</option>
            </select>
          </div>

          {/* Type de traitement */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Type de traitement
            </label>
            <select
              value={formData.treatmentType}
              onChange={(e) => setFormData(prev => ({ ...prev, treatmentType: e.target.value as TreatmentType }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
            >
              <option value="arrosage">Arrosage</option>
              <option value="fertilisation">Fertilisation</option>
              <option value="taille">Taille</option>
              <option value="rempotage">Rempotage</option>
              <option value="traitement">Traitement phytosanitaire</option>
              <option value="semis">Semis</option>
              <option value="bouturage">Bouturage</option>
              <option value="marcottage">Marcottage</option>
              <option value="greffage">Greffage</option>
              <option value="pincement">Pincement</option>
              <option value="tuteurage">Tuteurage</option>
              <option value="paillage">Paillage</option>
              <option value="binage">Binage</option>
              <option value="desherbage">Désherbage</option>
              <option value="pulverisation">Pulvérisation</option>
              <option value="drainage">Drainage</option>
              <option value="protection">Protection</option>
              <option value="nettoyage">Nettoyage</option>
              <option value="recolte">Récolte</option>
              <option value="division">Division</option>
              <option value="surfacage">Surfaçage</option>
              <option value="hivernage">Hivernage</option>
              <option value="revegetation">Réveil végétatif</option>
            </select>
          </div>

          {/* Date et heure */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Date et heure *
            </label>
            <input
              type="datetime-local"
              value={formData.nextActionDate.toISOString().slice(0, 16)}
              onChange={(e) => setFormData(prev => ({ ...prev, nextActionDate: new Date(e.target.value) }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
              required
            />
          </div>

          {/* Action à effectuer */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Action à effectuer (optionnel)
            </label>
            <input
              type="text"
              value={formData.nextActionType}
              onChange={(e) => setFormData(prev => ({ ...prev, nextActionType: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
              placeholder="Ex: Arroser abondamment, Appliquer traitement fongicide..."
            />
            <p className="text-xs text-gray-400 mt-1">
              Si vide, sera généré automatiquement selon le type d'événement
            </p>
          </div>

          {/* Priorité */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Priorité
            </label>
            <select
              value={formData.priority}
              onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
            >
              <option value="low">Faible</option>
              <option value="medium">Moyenne</option>
              <option value="high">Élevée</option>
              <option value="urgent">Urgente</option>
            </select>
          </div>

          {/* Récurrence */}
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.isRecurring}
                onChange={(e) => setFormData(prev => ({ ...prev, isRecurring: e.target.checked }))}
                className="rounded border-gray-600 text-[#d385f5] focus:ring-[#d385f5] bg-gray-800"
              />
              <span className="text-sm font-medium text-gray-300">Événement récurrent (rappel automatique)</span>
            </label>
          </div>

          {/* Options de récurrence */}
          {formData.isRecurring && (
            <div className="space-y-4 p-4 bg-gray-800/50 rounded-md border border-gray-600">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Fréquence
                  </label>
                  <select
                    value={formData.recurrenceFrequency}
                    onChange={(e) => setFormData(prev => ({ ...prev, recurrenceFrequency: e.target.value as any }))}
                    className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                  >
                    <option value="daily">Quotidienne</option>
                    <option value="weekly">Hebdomadaire</option>
                    <option value="monthly">Mensuelle</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Intervalle
                  </label>
                  <input
                    type="number"
                    value={formData.recurrenceInterval}
                    onChange={(e) => setFormData(prev => ({ ...prev, recurrenceInterval: parseInt(e.target.value) || 1 }))}
                    min="1"
                    max="30"
                    className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                    placeholder="Ex: 2 pour 'tous les 2 jours'"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Date de fin (optionnelle)
                </label>
                <input
                  type="date"
                  value={formData.recurrenceEndDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, recurrenceEndDate: e.target.value }))}
                  className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Laissez vide pour une récurrence illimitée
                </p>
              </div>
            </div>
          )}

          {/* Boutons */}
          <div className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={onClose}
              className="flex-1"
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.plantId || !formData.title}
              className="flex-1 bg-[#d385f5] hover:bg-[#c070e0] text-white"
            >
              {isSubmitting ? 'Création...' : 'Créer'}
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

/**
 * Composant modal pour modifier un événement existant
 */
const EditEventModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onUpdateEvent: (eventData: CreateDiagnosticEventData) => Promise<void>;
  event: DiagnosticEvent | null;
  plants: Plant[];
}> = ({ isOpen, onClose, onUpdateEvent, event, plants }) => {
  const [formData, setFormData] = useState({
    plantId: '',
    title: '',
    description: '',
    eventType: 'traitement' as 'diagnostic' | 'traitement' | 'suivi',
    treatmentType: 'arrosage' as TreatmentType,
    nextActionDate: new Date(),
    nextActionType: '',
    priority: 'medium' as 'low' | 'medium' | 'high' | 'urgent',
    isRecurring: false,
    recurrenceFrequency: 'weekly' as 'daily' | 'weekly' | 'monthly',
    recurrenceInterval: 1,
    recurrenceEndDate: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Initialiser le formulaire avec les données de l'événement
  useEffect(() => {
    if (event && isOpen) {
      setFormData({
        plantId: event.plantId,
        title: event.title,
        description: event.description,
        eventType: event.eventType,
        treatmentType: event.treatmentType,
        nextActionDate: event.nextActionDate.toDate(),
        nextActionType: event.nextActionType,
        priority: event.priority || 'medium',
        isRecurring: event.isRecurring || false,
        recurrenceFrequency: event.recurrencePattern?.frequency || 'weekly',
        recurrenceInterval: event.recurrencePattern?.interval || 1,
        recurrenceEndDate: event.recurrencePattern?.endDate?.toISOString().split('T')[0] || ''
      });
    }
  }, [event, isOpen]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!event) return;

    setIsSubmitting(true);
    try {
      await onUpdateEvent({
        userId: '', // Sera rempli par le composant parent
        plantId: formData.plantId,
        plantName: event.plantName, // Garder le nom de plante existant
        title: formData.title,
        description: formData.description,
        eventType: formData.eventType,
        treatmentType: formData.treatmentType,
        nextActionDate: formData.nextActionDate,
        nextActionType: formData.nextActionType || `${formData.eventType} - ${formData.treatmentType}`,
        priority: formData.priority,
        isRecurring: formData.isRecurring,
        recurrencePattern: formData.isRecurring ? {
          frequency: formData.recurrenceFrequency,
          interval: formData.recurrenceInterval,
          endDate: formData.recurrenceEndDate ? new Date(formData.recurrenceEndDate) : undefined
        } : undefined
      });
      onClose();
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la modification de l\'événement:', errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen || !event) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        className="bg-[#1c1a31] rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto"
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-bold text-white">Modifier l'événement</h2>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <XMarkIcon className="h-4 w-4" />
          </Button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Titre */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Titre
            </label>
            <input
              type="text"
              value={formData.title}
              onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
              required
            />
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white h-20"
              placeholder="Description détaillée..."
            />
          </div>

          {/* Date de prochaine action */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Date de prochaine action
            </label>
            <input
              type="datetime-local"
              value={formData.nextActionDate.toISOString().slice(0, 16)}
              onChange={(e) => setFormData(prev => ({ ...prev, nextActionDate: new Date(e.target.value) }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
              required
            />
          </div>

          {/* Action à effectuer */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Action à effectuer (optionnel)
            </label>
            <input
              type="text"
              value={formData.nextActionType}
              onChange={(e) => setFormData(prev => ({ ...prev, nextActionType: e.target.value }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
              placeholder="Ex: Arroser abondamment, Appliquer traitement fongicide..."
            />
            <p className="text-xs text-gray-400 mt-1">
              Si vide, sera généré automatiquement selon le type d'événement
            </p>
          </div>

          {/* Priorité */}
          <div>
            <label className="block text-sm font-medium text-gray-300 mb-1">
              Priorité
            </label>
            <select
              value={formData.priority}
              onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
              className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
            >
              <option value="low">Faible</option>
              <option value="medium">Moyenne</option>
              <option value="high">Élevée</option>
              <option value="urgent">Urgente</option>
            </select>
          </div>

          {/* Récurrence */}
          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={formData.isRecurring}
                onChange={(e) => setFormData(prev => ({ ...prev, isRecurring: e.target.checked }))}
                className="rounded border-gray-600 text-[#d385f5] focus:ring-[#d385f5] bg-gray-800"
              />
              <span className="text-sm font-medium text-gray-300">Événement récurrent (rappel automatique)</span>
            </label>
          </div>

          {/* Options de récurrence */}
          {formData.isRecurring && (
            <div className="space-y-4 p-4 bg-gray-800/50 rounded-md border border-gray-600">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Fréquence
                  </label>
                  <select
                    value={formData.recurrenceFrequency}
                    onChange={(e) => setFormData(prev => ({ ...prev, recurrenceFrequency: e.target.value as any }))}
                    className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                  >
                    <option value="daily">Quotidienne</option>
                    <option value="weekly">Hebdomadaire</option>
                    <option value="monthly">Mensuelle</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-300 mb-1">
                    Intervalle
                  </label>
                  <input
                    type="number"
                    value={formData.recurrenceInterval}
                    onChange={(e) => setFormData(prev => ({ ...prev, recurrenceInterval: parseInt(e.target.value) || 1 }))}
                    min="1"
                    max="30"
                    className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                    placeholder="Ex: 2 pour 'tous les 2 jours'"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Date de fin (optionnelle)
                </label>
                <input
                  type="date"
                  value={formData.recurrenceEndDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, recurrenceEndDate: e.target.value }))}
                  className="w-full p-2 bg-gray-800 border border-gray-600 rounded text-white"
                />
                <p className="text-xs text-gray-400 mt-1">
                  Laissez vide pour une récurrence illimitée
                </p>
              </div>
            </div>
          )}

          {/* Boutons */}
          <div className="flex gap-2 pt-4">
            <Button
              type="button"
              variant="ghost"
              onClick={onClose}
              className="flex-1"
            >
              Annuler
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.title}
              className="flex-1 bg-[#d385f5] hover:bg-[#c070e0] text-white"
            >
              {isSubmitting ? 'Modification...' : 'Modifier'}
            </Button>
          </div>
        </form>
      </motion.div>
    </div>
  );
};

/**
 * Composant principal de vue du calendrier
 * Affiche les événements de traitement des plantes avec différentes vues (mois, semaine, liste)
 */
const CalendarView: React.FC = () => {
  const { user } = useAuth();
  const [events, setEvents] = useState<DiagnosticEvent[]>([]);
  const [plants, setPlants] = useState<Plant[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [selectedEvent, setSelectedEvent] = useState<DiagnosticEvent | null>(null);
  const [viewOptions, setViewOptions] = useState({
    viewType: 'month' as 'month' | 'week' | 'day' | 'list',
    currentDate: new Date(),
    filters: {} as DiagnosticEventFilters,
    showCompleted: false,
    showOverdue: true
  });

  // Chargement des événements et des plantes
  useEffect(() => {
    if (!user) return;

    const unsubscribeEvents = notificationService.getDiagnosticEvents(user.uid, (fetchedEvents) => {
      setEvents(fetchedEvents);
      setIsLoading(false);
    }, viewOptions.filters);

    const unsubscribePlants = getPlants(user.uid, (fetchedPlants) => {
      setPlants(fetchedPlants);
    });

    return () => {
      unsubscribeEvents();
      unsubscribePlants();
    };
  }, [user, viewOptions.filters]);

  // Fonction pour créer un nouvel événement
  const handleCreateEvent = async (eventData: CreateDiagnosticEventData) => {
    if (!user) return;

    try {
      await notificationService.createDiagnosticEvent({
        ...eventData,
        userId: user.uid
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la création de l\'événement:', errorMessage);
      throw error;
    }
  };

  // Fonction pour ouvrir le modal de modification
  const handleEditEvent = (event: DiagnosticEvent) => {
    setSelectedEvent(event);
    setIsEditModalOpen(true);
  };

  // Fonction pour modifier un événement existant
  const handleUpdateEvent = async (eventData: CreateDiagnosticEventData) => {
    if (!user || !selectedEvent) return;

    try {
      await notificationService.updateDiagnosticEvent(user.uid, selectedEvent.id, {
        title: eventData.title,
        description: eventData.description,
        nextActionDate: eventData.nextActionDate,
        nextActionType: eventData.nextActionType,
        priority: eventData.priority
      });
      setIsEditModalOpen(false);
      setSelectedEvent(null);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      console.error('❌ Erreur lors de la modification de l\'événement:', errorMessage);
      throw error;
    }
  };



  // Filtrage des événements selon les options de vue
  const filteredEvents = events.filter(event => {
    const eventDate = event.nextActionDate.toDate();
    const now = new Date();

    // Filtrer par statut
    if (!viewOptions.showCompleted && event.status === 'completed') {
      return false;
    }
    if (!viewOptions.showOverdue && event.status === 'overdue') {
      return false;
    }

    // Filtrer par période selon le type de vue
    switch (viewOptions.viewType) {
      case 'day':
        return eventDate.toDateString() === viewOptions.currentDate.toDateString();
      case 'week':
        const weekStart = new Date(viewOptions.currentDate);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        return eventDate >= weekStart && eventDate <= weekEnd;
      case 'month':
        return eventDate.getMonth() === viewOptions.currentDate.getMonth() &&
               eventDate.getFullYear() === viewOptions.currentDate.getFullYear();
      default:
        return true;
    }
  });

  // Couleurs par type de traitement
  const getTreatmentColor = (type: TreatmentType): string => {
    const colors = {
      fertilisation: 'bg-green-100 text-green-800',
      traitement: 'bg-red-100 text-red-800',
      arrosage: 'bg-blue-100 text-blue-800',
      rempotage: 'bg-purple-100 text-purple-800',
      taille: 'bg-orange-100 text-orange-800'
    };
    return colors[type] || 'bg-gray-100 text-gray-800';
  };

  // Couleurs par statut
  const getStatusColor = (status: string): string => {
    const colors = {
      pending: 'bg-blue-100 text-blue-800',
      completed: 'bg-green-100 text-green-800',
      overdue: 'bg-red-100 text-red-800',
      skipped: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  // Navigation entre les périodes
  const navigatePeriod = (direction: 'prev' | 'next') => {
    const newDate = new Date(viewOptions.currentDate);
    
    switch (viewOptions.viewType) {
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
    }
    
    setViewOptions(prev => ({ ...prev, currentDate: newDate }));
  };

  // Formatage de la date pour l'affichage
  const formatDateHeader = (): string => {
    const date = viewOptions.currentDate;
    const options: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'long'
    };

    switch (viewOptions.viewType) {
      case 'day':
        return date.toLocaleDateString('fr-FR', { 
          weekday: 'long', 
          year: 'numeric', 
          month: 'long', 
          day: 'numeric' 
        });
      case 'week':
        const weekStart = new Date(date);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekEnd.getDate() + 6);
        return `${weekStart.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short' })} - ${weekEnd.toLocaleDateString('fr-FR', { day: 'numeric', month: 'short', year: 'numeric' })}`;
      case 'month':
        return date.toLocaleDateString('fr-FR', options);
      default:
        return 'Tous les événements';
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <BackgroundWrapper backgroundKey="calendar" overlayOpacity={0.5}>
      <div className="space-y-6 p-4 sm:p-6 pt-40">
        {/* En-tête avec contrôles */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-6 w-6 text-[#d385f5]" />
          <h1 className="text-2xl font-bold text-white">Calendrier des Traitements</h1>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            size="sm"
            onClick={() => setIsCreateModalOpen(true)}
            className="bg-[#d385f5] hover:bg-[#c070e0] text-white"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            Nouvel événement
          </Button>
        </div>
      </div>

      {/* Contrôles de vue */}
      <Card>
        <CardHeader className="pb-3">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigatePeriod('prev')}
              >
                ←
              </Button>
              <h2 className="text-lg font-semibold text-white">{formatDateHeader()}</h2>
              <Button
                variant="outline"
                size="sm"
                onClick={() => navigatePeriod('next')}
              >
                →
              </Button>
            </div>
            
            <div className="flex items-center gap-2">
              {(['day', 'week', 'month', 'list'] as const).map((view) => (
                <Button
                  key={view}
                  variant={viewOptions.viewType === view ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewOptions(prev => ({ ...prev, viewType: view }))}
                >
                  {view === 'day' ? 'Jour' : 
                   view === 'week' ? 'Semaine' : 
                   view === 'month' ? 'Mois' : 'Liste'}
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {filteredEvents.length === 0 ? (
            <div className="text-center py-8 text-gray-400">
              <CalendarIcon className="h-12 w-12 mx-auto mb-4 text-gray-600" />
              <p>Aucun événement pour cette période</p>
            </div>
          ) : (
            <div className="space-y-3">
              {filteredEvents.map((event) => (
                <div
                  key={event.id}
                  className="flex items-center justify-between p-4 border border-gray-700 rounded-lg hover:bg-gray-800/50 transition-colors"
                >
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="font-medium text-white">{event.title}</h3>
                      <Badge className={getTreatmentColor(event.treatmentType)}>
                        {event.treatmentType}
                      </Badge>
                      <Badge className={getStatusColor(event.status)}>
                        {event.status === 'pending' ? 'En attente' :
                         event.status === 'completed' ? 'Terminé' :
                         event.status === 'overdue' ? 'En retard' : 'Ignoré'}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-300 mb-1">{event.plantName}</p>
                    <p className="text-sm text-gray-400">
                      {event.nextActionDate.toDate().toLocaleDateString('fr-FR', {
                        weekday: 'short',
                        day: 'numeric',
                        month: 'short',
                        hour: '2-digit',
                        minute: '2-digit'
                      })}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">{event.nextActionType}</p>
                  </div>

                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditEvent(event)}
                    >
                      Modifier
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Modal de création d'événement */}
      <AnimatePresence>
        {isCreateModalOpen && (
          <CreateEventModal
            isOpen={isCreateModalOpen}
            onClose={() => setIsCreateModalOpen(false)}
            onCreateEvent={handleCreateEvent}
            plants={plants}
          />
        )}
      </AnimatePresence>

      {/* Modal de modification d'événement */}
      <AnimatePresence>
        {isEditModalOpen && (
          <EditEventModal
            isOpen={isEditModalOpen}
            onClose={() => {
              setIsEditModalOpen(false);
              setSelectedEvent(null);
            }}
            onUpdateEvent={handleUpdateEvent}
            event={selectedEvent}
            plants={plants}
          />
        )}
      </AnimatePresence>
      </div>
    </BackgroundWrapper>
  );
};

export default CalendarView;
