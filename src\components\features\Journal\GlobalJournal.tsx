import React, { useState, useMemo } from 'react';
import { Calendar, Search, Filter, Download, TrendingUp, Clock, CheckCircle, AlertTriangle } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../../ui/card';
import { Button } from '../../ui/button';
import { Badge } from '../../ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../ui/tabs';
import { useActionHistory, useDiagnosticEvents } from '../../../hooks/useNotifications';
import { UserActionHistory, DiagnosticEvent } from '../../../types/notifications';
import { formatDistanceToNow, format, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear } from 'date-fns';
import { fr } from 'date-fns/locale';
import { THEME_COLORS, THEME_CLASSES } from '../../../styles/colors';
import { BackgroundWrapper } from '../../common/BackgroundWrapper';

/**
 * Composant principal du journal global
 */
export const GlobalJournal: React.FC = () => {
  const { history, loading: historyLoading } = useActionHistory();
  const { events, loading: eventsLoading } = useDiagnosticEvents();
  const [activeTab, setActiveTab] = useState('timeline');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateFilter, setDateFilter] = useState<'all' | 'week' | 'month' | 'year'>('all');

  // Filtrage des données
  const filteredHistory = useMemo(() => {
    let filtered = history;

    // Filtre par terme de recherche
    if (searchTerm) {
      filtered = filtered.filter(item =>
        item.plantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        item.description.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Filtre par date
    if (dateFilter !== 'all') {
      const now = new Date();
      let startDate: Date;
      let endDate: Date;

      switch (dateFilter) {
        case 'week':
          startDate = startOfWeek(now);
          endDate = endOfWeek(now);
          break;
        case 'month':
          startDate = startOfMonth(now);
          endDate = endOfMonth(now);
          break;
        case 'year':
          startDate = startOfYear(now);
          endDate = endOfYear(now);
          break;
        default:
          startDate = new Date(0);
          endDate = new Date();
      }

      filtered = filtered.filter(item => {
        const itemDate = item.actionDate.toDate();
        return itemDate >= startDate && itemDate <= endDate;
      });
    }

    return filtered;
  }, [history, searchTerm, dateFilter]);

  // Statistiques
  const stats = useMemo(() => {
    const totalActions = history.length;
    const thisWeekActions = history.filter(item => {
      const itemDate = item.actionDate.toDate();
      const weekStart = startOfWeek(new Date());
      return itemDate >= weekStart;
    }).length;

    const diagnosticsCount = history.filter(item => item.actionType === 'diagnostic').length;
    const treatmentsCount = history.filter(item => item.actionType === 'treatment_applied').length;
    const completedEventsCount = history.filter(item => item.actionType === 'event_completed').length;

    const pendingEvents = events.filter(event => event.status === 'pending').length;
    const overdueEvents = events.filter(event => event.status === 'overdue').length;

    return {
      totalActions,
      thisWeekActions,
      diagnosticsCount,
      treatmentsCount,
      completedEventsCount,
      pendingEvents,
      overdueEvents
    };
  }, [history, events]);

  const loading = historyLoading || eventsLoading;

  // Fonction d'export
  const exportToCSV = () => {
    const csvData = filteredHistory.map(action => ({
      Date: format(action.actionDate.toDate(), 'dd/MM/yyyy HH:mm', { locale: fr }),
      Plante: action.plantName,
      Action: action.actionType,
      Description: action.description,
      Métadonnées: action.metadata ? JSON.stringify(action.metadata) : ''
    }));

    const csvContent = [
      Object.keys(csvData[0]).join(','),
      ...csvData.map(row => Object.values(row).map(val => `"${val}"`).join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `journal-florasynth-${format(new Date(), 'yyyy-MM-dd')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#d385f5]"></div>
      </div>
    );
  }

  return (
    <BackgroundWrapper backgroundKey="journal" overlayOpacity={0.6}>
      <div className="max-w-6xl mx-auto p-6 pt-20 space-y-6">
        {/* En-tête avec statistiques */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Calendar className="h-8 w-8 text-[#d385f5]" />
          <div>
            <h1 className="text-2xl font-bold text-white">Journal Global</h1>
            <p className="text-[#E0E0E0]">Historique complet de vos plantes</p>
          </div>
        </div>
        <Button
          variant="outline"
          className="flex items-center gap-2"
          onClick={exportToCSV}
          disabled={filteredHistory.length === 0}
        >
          <Download className="h-4 w-4" />
          Exporter CSV
        </Button>
      </div>

      {/* Statistiques rapides */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalActions}</div>
            <p className="text-xs text-gray-600">+{stats.thisWeekActions} cette semaine</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Diagnostics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.diagnosticsCount}</div>
            <p className="text-xs text-gray-600">analyses effectuées</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Événements en cours</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{stats.pendingEvents}</div>
            <p className="text-xs text-gray-600">{stats.overdueEvents} en retard</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Traitements</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.treatmentsCount}</div>
            <p className="text-xs text-gray-600">appliqués</p>
          </CardContent>
        </Card>
      </div>

      {/* Contrôles de filtrage */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Rechercher par plante ou action..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={dateFilter === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDateFilter('all')}
              >
                Tout
              </Button>
              <Button
                variant={dateFilter === 'week' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDateFilter('week')}
              >
                Cette semaine
              </Button>
              <Button
                variant={dateFilter === 'month' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDateFilter('month')}
              >
                Ce mois
              </Button>
              <Button
                variant={dateFilter === 'year' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setDateFilter('year')}
              >
                Cette année
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Contenu principal */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="timeline" className="flex items-center gap-2">
            <Clock className="h-4 w-4" />
            Chronologie
          </TabsTrigger>
          <TabsTrigger value="events" className="flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Événements
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="h-4 w-4" />
            Analyses
          </TabsTrigger>
        </TabsList>

        {/* Onglet Chronologie */}
        <TabsContent value="timeline" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5" />
                Historique des actions ({filteredHistory.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {filteredHistory.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucune action trouvée pour les critères sélectionnés</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {filteredHistory.map((action) => (
                    <ActionTimelineItem key={action.id} action={action} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Événements */}
        <TabsContent value="events" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="h-5 w-5" />
                Événements à venir
              </CardTitle>
            </CardHeader>
            <CardContent>
              {events.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Calendar className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <p>Aucun événement programmé</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {events.slice(0, 10).map((event) => (
                    <EventTimelineItem key={event.id} event={event} />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Onglet Analyses */}
        <TabsContent value="analytics" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Analyses et tendances
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8 text-gray-500">
                <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Graphiques et analyses à venir...</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      </div>
    </BackgroundWrapper>
  );
};

/**
 * Composant pour afficher une action dans la timeline
 */
interface ActionTimelineItemProps {
  action: UserActionHistory;
}

const ActionTimelineItem: React.FC<ActionTimelineItemProps> = ({ action }) => {
  const getActionIcon = (actionType: string) => {
    switch (actionType) {
      case 'diagnostic': return <AlertTriangle className="h-4 w-4 text-blue-600" />;
      case 'treatment_applied': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'event_completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'event_skipped': return <Clock className="h-4 w-4 text-gray-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getActionLabel = (actionType: string) => {
    switch (actionType) {
      case 'diagnostic': return 'Diagnostic effectué';
      case 'treatment_applied': return 'Traitement appliqué';
      case 'event_completed': return 'Événement terminé';
      case 'event_skipped': return 'Événement ignoré';
      default: return 'Action inconnue';
    }
  };

  const getActionColor = (actionType: string) => {
    switch (actionType) {
      case 'diagnostic': return 'border-l-blue-500 bg-blue-50';
      case 'treatment_applied': return 'border-l-green-500 bg-green-50';
      case 'event_completed': return 'border-l-green-500 bg-green-50';
      case 'event_skipped': return 'border-l-gray-500 bg-gray-50';
      default: return 'border-l-gray-500 bg-gray-50';
    }
  };

  return (
    <div className={`p-4 border-l-4 rounded-r-lg ${getActionColor(action.actionType)}`}>
      <div className="flex items-start gap-3">
        {getActionIcon(action.actionType)}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-2">
              <span className="font-medium text-white">{getActionLabel(action.actionType)}</span>
              <Badge variant="outline" className="text-xs text-[#E0E0E0] border-[#d385f5]">
                {action.plantName}
              </Badge>
            </div>
            <span className="text-xs text-gray-400">
              {formatDistanceToNow(action.actionDate.toDate(), { addSuffix: true, locale: fr })}
            </span>
          </div>
          <p className="text-[#E0E0E0] text-sm mb-2">{action.description}</p>
          <div className="text-xs text-gray-400">
            {format(action.actionDate.toDate(), 'dd/MM/yyyy à HH:mm', { locale: fr })}
          </div>
          {action.metadata && Object.keys(action.metadata).length > 0 && (
            <div className="mt-2 text-xs text-gray-600">
              {action.metadata.disease && (
                <span className="inline-block bg-red-100 text-red-800 px-2 py-1 rounded mr-2">
                  Maladie: {action.metadata.disease}
                </span>
              )}
              {action.metadata.confidence && (
                <span className="inline-block bg-blue-100 text-blue-800 px-2 py-1 rounded">
                  Confiance: {Math.round(action.metadata.confidence * 100)}%
                </span>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

/**
 * Composant pour afficher un événement dans la timeline
 */
interface EventTimelineItemProps {
  event: DiagnosticEvent;
}

const EventTimelineItem: React.FC<EventTimelineItemProps> = ({ event }) => {
  const isOverdue = event.status === 'overdue' ||
    (event.status === 'pending' && event.nextActionDate.toDate() < new Date());

  const getStatusIcon = () => {
    switch (event.status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'overdue': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'pending': return isOverdue ?
        <AlertTriangle className="h-4 w-4 text-red-600" /> :
        <Clock className="h-4 w-4 text-blue-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getEventColor = () => {
    if (event.status === 'completed') return 'border-l-green-500 bg-green-50';
    if (isOverdue) return 'border-l-red-500 bg-red-50';
    return 'border-l-blue-500 bg-blue-50';
  };

  return (
    <div className={`p-4 border-l-4 rounded-r-lg ${getEventColor()}`}>
      <div className="flex items-start gap-3">
        {getStatusIcon()}
        <div className="flex-1">
          <div className="flex items-center justify-between mb-1">
            <div className="flex items-center gap-2">
              <span className="font-medium text-white">{event.title}</span>
              <Badge variant="outline" className="text-xs text-[#E0E0E0] border-[#d385f5]">
                {event.plantName}
              </Badge>
              <Badge
                variant={event.priority === 'urgent' ? 'destructive' : 'secondary'}
                className="text-xs"
              >
                {event.priority}
              </Badge>
            </div>
            <span className="text-xs text-gray-400">
              {formatDistanceToNow(event.nextActionDate.toDate(), { addSuffix: true, locale: fr })}
            </span>
          </div>
          <p className="text-[#E0E0E0] text-sm mb-2">{event.description}</p>
          <div className="flex items-center gap-4 text-xs text-gray-600">
            <span>Action: {event.nextActionType}</span>
            <span>
              Prévu le {format(event.nextActionDate.toDate(), 'dd/MM/yyyy', { locale: fr })}
              {isOverdue && <span className="text-red-600 ml-1">(En retard)</span>}
            </span>
          </div>
          {event.geminiRecommendation && (
            <div className="mt-2 p-2 bg-blue-100 border border-blue-200 rounded text-xs">
              <div className="font-medium text-blue-800 mb-1">Recommandation Gemini:</div>
              <p className="text-blue-700">{event.geminiRecommendation}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default GlobalJournal;
